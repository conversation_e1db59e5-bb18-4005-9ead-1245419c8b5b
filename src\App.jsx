import { useState, useEffect } from 'react'
import { PanelGroup, Panel, PanelResizeHandle } from 'react-resizable-panels'
import Header from './components/Header'
import TabBar from './components/TabBar'
import CodeEditor from './components/CodeEditor'
import OutputPanel from './components/OutputPanel'
import Toolbar from './components/Toolbar'
import Footer from './components/Footer'
import { useCodeEditor } from './hooks/useCodeEditor'
import { useMobileLayout } from './hooks/useMobileLayout'
import './App.css'

function App() {
  const {
    activeTab,
    setActiveTab,
    code,
    updateCode,
    runCode,
    output,
    isRunning,
    error,
    programState,
    handleUserInput,
    stopExecution,
    saveProject,
    loadProject,
    newProject
  } = useCodeEditor()

  const [isOutputVisible, setIsOutputVisible] = useState(true)

  const {
    isMobileLayout,
    isTransitioning,
    toggleMobileLayout,
    shouldSuggestMobileLayout
  } = useMobileLayout()

  return (
    <div className={`app ${isMobileLayout ? 'mobile-layout' : 'desktop-layout'} ${isTransitioning ? 'transitioning' : ''}`}>
      <Header
        onSave={saveProject}
        onLoad={loadProject}
        onNew={newProject}
        isMobileLayout={isMobileLayout}
        onToggleMobileLayout={toggleMobileLayout}
      />

      <div className="app-content">
        <div className="editor-section">
          <TabBar
            activeTab={activeTab}
            onTabChange={setActiveTab}
            isMobileLayout={isMobileLayout}
          />

          <div className="editor-toolbar">
            <Toolbar
              onRun={() => runCode(activeTab)}
              isRunning={isRunning}
              onToggleOutput={() => setIsOutputVisible(!isOutputVisible)}
              isOutputVisible={isOutputVisible}
              isMobileLayout={isMobileLayout}
            />
          </div>

          <PanelGroup
            direction={isMobileLayout ? "vertical" : "horizontal"}
            className={`panel-group ${isMobileLayout ? 'mobile-panels' : 'desktop-panels'}`}
          >
            <Panel
              defaultSize={isMobileLayout ? 60 : 50}
              minSize={isMobileLayout ? 40 : 30}
              className="editor-panel"
            >
              <CodeEditor
                language={activeTab}
                value={code[activeTab]}
                onChange={(value) => updateCode(activeTab, value)}
              />
            </Panel>

            {isOutputVisible && (
              <>
                <PanelResizeHandle className={`panel-resize-handle ${isMobileLayout ? 'mobile-handle' : 'desktop-handle'}`} />
                <Panel
                  defaultSize={isMobileLayout ? 40 : 50}
                  minSize={isMobileLayout ? 30 : 30}
                  className="output-panel"
                >
                  <OutputPanel
                    output={output}
                    error={error}
                    isRunning={isRunning}
                    activeTab={activeTab}
                    programState={programState}
                    onUserInput={handleUserInput}
                    onStopExecution={stopExecution}
                    onExecute={() => runCode(activeTab)}
                    isMobileLayout={isMobileLayout}
                  />
                </Panel>
              </>
            )}
          </PanelGroup>
        </div>
      </div>

      <Footer isMobileLayout={isMobileLayout} />
    </div>
  )
}

export default App
