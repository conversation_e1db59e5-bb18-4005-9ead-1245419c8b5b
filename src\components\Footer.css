.footer {
  position: relative;
  background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-tertiary) 100%);
  border-top: 1px solid var(--border-primary);
  padding: 1rem 1.5rem;
  min-height: 60px;
  overflow: hidden;
  backdrop-filter: blur(10px);
  transition: all var(--transition-normal);
}

.footer:hover {
  background: linear-gradient(135deg, var(--bg-tertiary) 0%, var(--bg-secondary) 100%);
  border-top-color: var(--border-secondary);
}

.footer-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
  z-index: 2;
  max-width: 1400px;
  margin: 0 auto;
}

.footer-left {
  flex: 1;
  display: flex;
  align-items: center;
}

.credits-container {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.5rem 1rem;
  background: rgba(37, 99, 235, 0.1);
  border: 1px solid rgba(37, 99, 235, 0.2);
  border-radius: var(--radius-lg);
  backdrop-filter: blur(8px);
  transition: all var(--transition-normal);
  cursor: pointer;
}

.credits-container:hover {
  background: rgba(37, 99, 235, 0.15);
  border-color: rgba(37, 99, 235, 0.3);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(37, 99, 235, 0.2);
}

.credits-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
  border-radius: var(--radius-md);
  box-shadow: 0 2px 8px rgba(37, 99, 235, 0.3);
}

.code-icon {
  color: white;
  animation: pulse-glow 3s ease-in-out infinite;
}

.credits-text {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.credits-main {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-primary);
  line-height: 1.4;
}

.developer-name {
  background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  font-weight: 700;
  position: relative;
}

.developer-name::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
  border-radius: 1px;
  transform: scaleX(0);
  transition: transform var(--transition-normal);
}

.credits-container:hover .developer-name::after {
  transform: scaleX(1);
}

.credits-subtitle {
  display: flex;
  align-items: center;
  gap: 0.375rem;
  font-size: 0.75rem;
  color: var(--text-muted);
  font-weight: 400;
}

.sparkle-icon {
  color: var(--accent-color);
  animation: sparkle 2s ease-in-out infinite;
}

.footer-center {
  flex: 1;
  display: flex;
  justify-content: center;
}

.tech-stack {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.375rem 0.75rem;
  background: rgba(16, 185, 129, 0.1);
  border: 1px solid rgba(16, 185, 129, 0.2);
  border-radius: var(--radius-md);
  backdrop-filter: blur(8px);
}

.tech-item {
  font-size: 0.75rem;
  font-weight: 500;
  color: var(--accent-color);
  transition: color var(--transition-fast);
}

.tech-item:hover {
  color: var(--text-primary);
}

.tech-divider {
  color: var(--text-muted);
  font-size: 0.75rem;
}

.footer-right {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 0.25rem;
}

.made-with-love {
  display: flex;
  align-items: center;
  gap: 0.375rem;
  font-size: 0.75rem;
  color: var(--text-secondary);
  font-weight: 500;
}

.heart-icon {
  color: #ef4444;
  animation: heartbeat 2s ease-in-out infinite;
}

.version-info {
  font-size: 0.6875rem;
  color: var(--text-muted);
  font-family: var(--font-family-mono);
  padding: 0.125rem 0.375rem;
  background: var(--bg-primary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-sm);
}

/* Animated background elements */
.footer-bg-elements {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  overflow: hidden;
}

.bg-element {
  position: absolute;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(37, 99, 235, 0.1), rgba(16, 185, 129, 0.1));
  animation: float 6s ease-in-out infinite;
}

.bg-element-1 {
  width: 60px;
  height: 60px;
  top: -30px;
  left: 10%;
  animation-delay: 0s;
}

.bg-element-2 {
  width: 40px;
  height: 40px;
  top: -20px;
  right: 20%;
  animation-delay: 2s;
}

.bg-element-3 {
  width: 80px;
  height: 80px;
  bottom: -40px;
  left: 60%;
  animation-delay: 4s;
}

/* Animations */
@keyframes pulse-glow {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 2px 8px rgba(37, 99, 235, 0.3);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 4px 16px rgba(37, 99, 235, 0.5);
  }
}

@keyframes sparkle {
  0%, 100% {
    opacity: 0.7;
    transform: scale(1) rotate(0deg);
  }
  50% {
    opacity: 1;
    transform: scale(1.2) rotate(180deg);
  }
}

@keyframes heartbeat {
  0%, 100% {
    transform: scale(1);
  }
  25% {
    transform: scale(1.1);
  }
  50% {
    transform: scale(1);
  }
  75% {
    transform: scale(1.05);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
    opacity: 0.3;
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
    opacity: 0.6;
  }
}

/* Hover effect for footer background elements */
.footer:hover .bg-element {
  animation-duration: 4s;
  opacity: 0.8;
}

/* Mobile Layout Specific Styles */
.footer.mobile-footer {
  min-height: 32px;
  padding: 0.25rem 1rem;
  transition: all var(--transition-normal);
  border-top: 2px solid var(--border-secondary);
  background: linear-gradient(135deg, var(--bg-tertiary) 0%, var(--bg-secondary) 100%);
}

.footer.mobile-footer.collapsed {
  min-height: 24px;
  padding: 0.125rem 1rem;
  opacity: 0.8;
}

.footer.mobile-footer .footer-content {
  flex-direction: row;
  gap: 0.5rem;
  align-items: center;
  justify-content: center;
  transition: all var(--transition-normal);
}

.footer.mobile-footer.collapsed .footer-content {
  opacity: 0;
  transform: translateY(-10px);
  pointer-events: none;
}

.footer.mobile-footer .footer-left,
.footer.mobile-footer .footer-center,
.footer.mobile-footer .footer-right {
  flex: none;
  width: auto;
}

.footer.mobile-footer .footer-left {
  justify-content: flex-start;
}

.footer.mobile-footer .footer-center {
  display: none; /* Hide tech stack in mobile to save space */
}

.footer.mobile-footer .footer-right {
  align-items: center;
  flex-direction: row;
  gap: 0.5rem;
}

.footer.mobile-footer .credits-container {
  padding: 0.25rem 0.5rem;
  gap: 0.375rem;
  background: rgba(37, 99, 235, 0.08);
  border: 1px solid rgba(37, 99, 235, 0.15);
}

.footer.mobile-footer .credits-main {
  font-size: 0.6875rem;
  text-align: left;
}

.footer.mobile-footer .credits-subtitle {
  display: none; /* Hide subtitle in mobile to save space */
}

.footer.mobile-footer .credits-icon {
  width: 24px;
  height: 24px;
}

.footer.mobile-footer .made-with-love {
  font-size: 0.625rem;
}

.footer.mobile-footer .version-info {
  font-size: 0.625rem;
  padding: 0.125rem 0.25rem;
}

.footer.mobile-footer .bg-element {
  display: none;
}

/* Footer Toggle Button */
.footer-toggle {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.375rem;
  padding: 0.25rem 0.75rem;
  background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
  color: white;
  font-size: 0.6875rem;
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition-fast);
  border-radius: 0;
  border-bottom: 1px solid var(--primary-hover);
  position: relative;
  z-index: 10;
}

.footer-toggle:hover {
  background: linear-gradient(135deg, var(--primary-hover), var(--primary-color));
  transform: translateY(-1px);
}

.footer-toggle:active {
  transform: translateY(0);
}

.toggle-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  transition: all var(--transition-fast);
}

.footer-toggle:hover .toggle-indicator {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

.toggle-text {
  font-weight: 600;
  letter-spacing: 0.025em;
}

/* Desktop Footer Styles */
.footer.desktop-footer {
  /* Keep existing desktop styles */
}

/* Responsive Design Fallback */
@media (max-width: 768px) {
  .footer:not(.mobile-footer) {
    padding: 0.75rem 1rem;
  }

  .footer:not(.mobile-footer) .footer-content {
    flex-direction: column;
    gap: 0.75rem;
    align-items: center;
  }

  .footer:not(.mobile-footer) .footer-left,
  .footer:not(.mobile-footer) .footer-center,
  .footer:not(.mobile-footer) .footer-right {
    flex: none;
    width: 100%;
  }

  .footer:not(.mobile-footer) .footer-left {
    justify-content: center;
  }

  .footer:not(.mobile-footer) .footer-right {
    align-items: center;
  }

  .footer:not(.mobile-footer) .credits-container {
    padding: 0.375rem 0.75rem;
    gap: 0.5rem;
  }

  .footer:not(.mobile-footer) .credits-main {
    font-size: 0.75rem;
    text-align: center;
  }

  .footer:not(.mobile-footer) .credits-subtitle {
    font-size: 0.6875rem;
    justify-content: center;
  }

  .footer:not(.mobile-footer) .tech-stack {
    justify-content: center;
    flex-wrap: wrap;
    gap: 0.375rem;
  }

  .footer:not(.mobile-footer) .tech-item {
    font-size: 0.6875rem;
  }

  .footer:not(.mobile-footer) .bg-element {
    display: none;
  }
}

@media (max-width: 480px) {
  .footer {
    padding: 0.5rem 0.75rem;
  }

  .credits-container {
    flex-direction: column;
    text-align: center;
    gap: 0.375rem;
  }

  .credits-text {
    align-items: center;
  }

  .tech-stack {
    padding: 0.25rem 0.5rem;
  }

  .made-with-love {
    font-size: 0.6875rem;
  }
}
